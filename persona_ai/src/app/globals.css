@import "tailwindcss";

:root {
  /* Updated color tokens to match Instagram-like chat design brief */
  --background: oklch(1 0 0); /* #ffffff - Main background color */
  --foreground: oklch(0.35 0.01 258.34); /* #475569 - Text color */
  --card: oklch(0.98 0.01 342.55); /* #fdf2f8 - Chat bubbles background */
  --card-foreground: oklch(0.35 0.01 258.34); /* #475569 - Chat bubbles text color */
  --popover: oklch(0.65 0.25 330.39); /* #ec4899 - Reaction popover background */
  --popover-foreground: oklch(1 0 0); /* #ffffff - Reaction popover text color */
  --primary: oklch(0.45 0.24 15.34); /* #be123c - Call-to-action buttons */
  --primary-foreground: oklch(1 0 0); /* #ffffff - Text color on primary buttons */
  --secondary: oklch(0.98 0.01 342.55); /* #fdf2f8 - Secondary elements */
  --secondary-foreground: oklch(0.35 0.01 258.34); /* #475569 - Text color on secondary elements */
  --muted: oklch(0.99 0 0); /* #f8fafc - Muted background */
  --muted-foreground: oklch(0.35 0.01 258.34); /* #475569 - Color for muted text */
  --accent: oklch(0.65 0.25 330.39); /* #ec4899 - Accent color for highlights */
  --accent-foreground: oklch(1 0 0); /* #ffffff - Text color for accent elements */
  --destructive: oklch(0.55 0.22 12.17); /* #e11d48 - Color for destructive actions */
  --destructive-foreground: oklch(1 0 0); /* #ffffff - Text color for destructive actions */
  --border: oklch(0.92 0 0); /* #e5e7eb - Border color for bubbles */
  --input: oklch(1 0 0); /* #ffffff - Input field background */
  --ring: oklch(0.65 0.25 330.39); /* #ec4899 - Focus ring color */
  --chart-1: oklch(0.45 0.24 15.34); /* #be123c - Chart color 1 */
  --chart-2: oklch(0.65 0.25 330.39); /* #ec4899 - Chart color 2 */
  --chart-3: oklch(0.98 0.01 342.55); /* #fdf2f8 - Chart color 3 */
  --chart-4: oklch(0.35 0.01 258.34); /* #475569 - Chart color 4 */
  --chart-5: oklch(0.99 0 0); /* #f8fafc - Chart color 5 */
  --radius: 0.5rem; /* Rounded corners for chat bubbles */
  --sidebar: oklch(1 0 0); /* #ffffff - Sidebar background */
  --sidebar-foreground: oklch(0.35 0.01 258.34); /* #475569 - Sidebar text color */
  --sidebar-primary: oklch(0.65 0.25 330.39); /* #ec4899 - Sidebar primary action color */
  --sidebar-primary-foreground: oklch(1 0 0); /* #ffffff - Sidebar primary action text color */
  --sidebar-accent: oklch(0.98 0.01 342.55); /* #fdf2f8 - Sidebar accent color */
  --sidebar-accent-foreground: oklch(0.35 0.01 258.34); /* #475569 - Sidebar accent text color */
  --sidebar-border: oklch(0.92 0 0); /* #e5e7eb - Sidebar border color */
  --sidebar-ring: oklch(0.65 0.25 330.39); /* #ec4899 - Sidebar focus ring color */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Added custom animations for chat interface */
@keyframes typing {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.typing-dot {
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Added landing page specific animations and styles */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

.shimmer-effect {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.slide-in-left {
  animation: slideInFromLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.scale-in {
  animation: scaleIn 0.4s ease-out;
}

/* Enhanced hover effects for persona cards */
.persona-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.persona-card:hover {
  transform: translateY(-8px);
}

.persona-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.persona-card:hover::before {
  opacity: 1;
}

/* Smooth gradient text animation */
.gradient-text {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Adding chai-themed dynamic animations for landing page */
@keyframes steam {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-3px) rotate(2deg);
    opacity: 1;
  }
}

@keyframes bounce-gentle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes gradient-x {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-gentle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float-slow {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-8px) rotate(2deg);
  }
}

@keyframes bounce-code {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-4px) scale(1.05);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-dot {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes border-glow {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.2);
  }
}

@keyframes heartbeat {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Animation utility classes */
.animate-steam {
  animation: steam 2s ease-in-out infinite;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

.animate-twinkle {
  animation: twinkle 1.5s ease-in-out infinite;
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 3s ease infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
  opacity: 0;
}

.animate-pulse-gentle {
  animation: pulse-gentle 3s ease-in-out infinite;
}

.animate-slide-in-left {
  animation: slide-in-left 0.8s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.8s ease-out forwards;
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 4s ease-in-out infinite;
}

.animate-bounce-code {
  animation: bounce-code 2s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

.animate-pulse-dot {
  animation: pulse-dot 2s ease-in-out infinite;
}

.animate-border-glow {
  animation: border-glow 2s ease-in-out infinite;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

.animate-in {
  animation: animateIn 0.2s ease-out;
}

.fade-in-0 {
  animation: fadeIn 0.2s ease-out;
}

.zoom-in-95 {
  animation: zoomIn 0.2s ease-out;
}

@keyframes animateIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    transform: scale(0.95);
  }
  to {
    transform: scale(1);
  }
}
