'use client';

import { useState } from 'react';
import ChatInterface from '@/components/ChatInterface';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface Persona {
  name: string;
  avatar: string;
  description: string;
  personality: string;
  tagline: string;
  color: string;
  features: string[];
}

const personas: <PERSON>a[] = [
  {
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    description: "Friendly AI Assistant",
    personality: "Hi! I'm <PERSON>, your friendly AI assistant. I'm here to help you with anything you need - from answering questions to having engaging conversations. I'm curious, helpful, and always ready to learn something new!",
    tagline: "Your helpful companion",
    color: "bg-blue-500",
    features: ["General Knowledge", "Problem Solving", "Friendly Chat"]
  },
  {
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    description: "Creative Writing Partner",
    personality: "Hello! I'm <PERSON>, your creative writing companion. I love storytelling, poetry, and helping bring your creative ideas to life. Whether you need inspiration, feedback, or just want to explore imaginative worlds together, I'm here!",
    tagline: "Unleash your creativity",
    color: "bg-purple-500",
    features: ["Creative Writing", "Storytelling", "Poetry"]
  },
  {
    name: "Max",
    avatar: "/placeholder.svg",
    description: "Tech & Code Expert",
    personality: "Hey there! I'm Max, your tech-savvy coding buddy. I'm passionate about programming, technology, and solving complex problems. Whether you're debugging code, learning new frameworks, or discussing the latest tech trends, I'm your go-to AI!",
    tagline: "Code, create, innovate",
    color: "bg-green-500",
    features: ["Programming", "Tech Support", "Code Review"]
  }
];

export default function Home() {
  const [selectedPersona, setSelectedPersona] = useState<Persona | null>(null);

  if (selectedPersona) {
    return (
      <ChatInterface
        initialPersona={selectedPersona}
        onBack={() => setSelectedPersona(null)}
      />
    );
  }

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4 animate-fade-in-up">
            Choose Your AI Persona
          </h1>
          <p className="text-muted-foreground text-lg animate-fade-in-up">
            Select an AI personality to start your conversation
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {personas.map((persona, index) => (
            <Card
              key={persona.name}
              className="persona-card relative overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-lg animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => setSelectedPersona(persona)}
            >
              <div className="p-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={persona.avatar} alt={persona.name} />
                    <AvatarFallback className={`${persona.color} text-white text-xl font-bold`}>
                      {persona.name[0]}
                    </AvatarFallback>
                  </Avatar>

                  <div>
                    <h3 className="text-xl font-semibold text-card-foreground mb-1">
                      {persona.name}
                    </h3>
                    <p className="text-muted-foreground text-sm mb-2">
                      {persona.description}
                    </p>
                    <p className="text-xs text-accent font-medium">
                      {persona.tagline}
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-2 justify-center">
                    {persona.features.map((feature) => (
                      <span
                        key={feature}
                        className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-full"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>

                  <Button
                    className="w-full mt-4"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedPersona(persona);
                    }}
                  >
                    Chat with {persona.name}
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
